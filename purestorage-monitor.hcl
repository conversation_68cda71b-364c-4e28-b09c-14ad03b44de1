job "purestorage-monitor" {
  datacenters = ["dc1"]
  type        = "service"
  
  constraint {
    attribute = "${driver.docker}"
    value     = "1"
  }

  group "monitor" {
    count = 1

    restart {
      attempts = 3
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    update {
      max_parallel     = 1
      min_healthy_time = "30s"
      healthy_deadline = "3m"
      auto_revert      = true
    }

    network {
      mode = "bridge"
    }

    task "purestorage-collector" {
      driver = "docker"

      config {
        image = "your-registry.com/purestorage-monitor:latest"
        command = "python"
        args = ["-m", "src.main", "--continuous", "--config", "/app/config.yaml"]

        mount {
          type     = "bind"
          source   = "local/config.yaml"
          target   = "/app/config.yaml"
          readonly = true
        }

        mount {
          type     = "bind"
          source   = "secrets/private.pem"
          target   = "/app/private.pem"
          readonly = true
        }

        mount {
          type     = "bind"
          source   = "secrets/public.pem"
          target   = "/app/public.pem"
          readonly = true
        }

        logging {
          type = "json-file"
          config {
            max-file = "3"
            max-size = "10m"
          }
        }
      }

      resources {
        cpu    = 250
        memory = 512
      }

      env {
        PYTHONPATH       = "/app"
        PYTHONUNBUFFERED = "1"
        TZ               = "Australia/Sydney"
      }

      template {
        data = <<EOH
purestorage:
  app_id: "{{ key "purestorage/app_id" }}"
  private_key_path: private.pem
  public_key_path: public.pem
  private_key_passphrase: "{{ key "purestorage/private_key_passphrase" }}"
  api_base_url: https://api.pure1.purestorage.com
  token_expiry_days: 30

otlp:
  enabled: true
  endpoint: "{{ key "purestorage/otlp_endpoint" }}"
  headers: {}
  username: "{{ key "purestorage/otlp_username" }}"
  password: "{{ key "purestorage/otlp_password" }}"
  timeout: 30
  service_name: purestorage-health-monitor

log_reporting:
  enabled: false
  level: INFO

monitoring:
  collection_interval: 300
  max_alerts: 1000
  max_arrays: 100
  max_hardware: 500
  max_drives: 1000
  max_controllers: 200
  max_volumes: 1500
  alert_severities:
  - critical
  - warning
  alert_states:
  - open
  metrics_enabled: true
  metrics_resolution: 300000
  metrics_history_hours: 24

logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  file: purestorage_monitor.log
  max_size: 10485760
  backup_count: 5
EOH
        destination = "local/config.yaml"
        change_mode = "restart"
      }

      template {
        data = <<EOH
{{ key "purestorage/ssl/private_key" }}
EOH
        destination = "secrets/private.pem"
        perms       = "600"
        change_mode = "restart"
      }

      template {
        data = <<EOH
{{ key "purestorage/ssl/public_key" }}
EOH
        destination = "secrets/public.pem"
        perms       = "644"
        change_mode = "restart"
      }

      service {
        name = "purestorage-monitor"
        tags = [
          "monitoring",
          "purestorage",
          "health-collector"
        ]

        check {
          type     = "script"
          name     = "purestorage-auth-check"
          command  = "python"
          args     = ["-m", "src.main", "--test-auth", "--config", "/app/config.yaml"]
          interval = "300s"
          timeout  = "60s"
        }
      }

      kill_timeout = "30s"
      kill_signal  = "SIGTERM"
    }
  }
}
