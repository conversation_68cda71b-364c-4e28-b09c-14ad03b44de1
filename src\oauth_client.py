"""
OAuth 2.0 Token Exchange Client for PureStorage API

This module handles the OAuth 2.0 token exchange process to convert
JWT tokens into access tokens for the PureStorage Pure1 API.
"""

import requests
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import logging
import json

logger = logging.getLogger(__name__)


class OAuthClient:
    """OAuth 2.0 client for PureStorage API token exchange."""
    
    def __init__(self, base_url: str = "https://api.pure1.purestorage.com"):
        """
        Initialize the OAuth client.
        
        Args:
            base_url: Base URL for the PureStorage API
        """
        self.base_url = base_url.rstrip('/')
        self.token_endpoint = f"{self.base_url}/oauth2/1.0/token"
        self._access_token = None
        self._token_expires_at = None
        self._session = requests.Session()
        
        # Set default headers
        self._session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
            'User-Agent': 'PureStorage-Health-Monitor/1.0'
        })
    
    def exchange_token(self, jwt_token: str) -> Dict[str, Any]:
        """
        Exchange a JWT token for an access token using OAuth 2.0 Token Exchange.
        
        Args:
            jwt_token: The JWT token to exchange
            
        Returns:
            Dictionary containing access token and metadata
            
        Raises:
            requests.RequestException: If the token exchange fails
        """
        payload = {
            'grant_type': 'urn:ietf:params:oauth:grant-type:token-exchange',
            'subject_token': jwt_token,
            'subject_token_type': 'urn:ietf:params:oauth:token-type:jwt'
        }
        
        try:
            logger.info("Attempting token exchange...")
            response = self._session.post(
                self.token_endpoint,
                data=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                token_data = response.json()
                
                # Store the access token and calculate expiry
                self._access_token = token_data['access_token']
                expires_in = token_data.get('expires_in', 3600)  # Default 1 hour
                self._token_expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
                
                logger.info(f"Token exchange successful. Token expires at {self._token_expires_at}")
                return token_data
                
            else:
                error_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {}
                error_msg = f"Token exchange failed: {response.status_code} - {error_data.get('error_description', response.text)}"
                logger.error(error_msg)
                raise requests.RequestException(error_msg)
                
        except requests.RequestException as e:
            logger.error(f"Token exchange request failed: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse token exchange response: {e}")
            raise requests.RequestException(f"Invalid JSON response: {e}")
    
    def get_access_token(self) -> Optional[str]:
        """
        Get the current access token.
        
        Returns:
            Access token string or None if not available
        """
        return self._access_token
    
    def is_token_valid(self) -> bool:
        """
        Check if the current access token is valid and not expired.
        
        Returns:
            True if token is valid, False otherwise
        """
        if not self._access_token or not self._token_expires_at:
            return False
        
        # Add 5 minute buffer before expiry
        buffer_time = timedelta(minutes=5)
        return datetime.utcnow() < (self._token_expires_at - buffer_time)
    
    def get_token_info(self) -> Dict[str, Any]:
        """
        Get information about the current access token.
        
        Returns:
            Dictionary with token information
        """
        return {
            'has_token': self._access_token is not None,
            'is_valid': self.is_token_valid(),
            'expires_at': self._token_expires_at.isoformat() if self._token_expires_at else None,
            'expires_in_seconds': int((self._token_expires_at - datetime.utcnow()).total_seconds()) if self._token_expires_at else None
        }
    
    def get_auth_headers(self) -> Dict[str, str]:
        """
        Get authorization headers for API requests.
        
        Returns:
            Dictionary with authorization headers
            
        Raises:
            ValueError: If no valid access token is available
        """
        if not self.is_token_valid():
            raise ValueError("No valid access token available. Please exchange a JWT token first.")
        
        return {
            'Authorization': f'Bearer {self._access_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def make_authenticated_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Make an authenticated request to the API.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            url: URL to request
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            ValueError: If no valid access token is available
            requests.RequestException: If the request fails
        """
        if not self.is_token_valid():
            raise ValueError("No valid access token available. Please exchange a JWT token first.")
        
        # Add authorization headers
        headers = kwargs.get('headers', {})
        headers.update(self.get_auth_headers())
        kwargs['headers'] = headers
        
        # Add default timeout if not specified
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30
        
        try:
            response = self._session.request(method, url, **kwargs)
            
            # Log rate limit information if available
            if 'X-RateLimit-Remaining-minute' in response.headers:
                remaining = response.headers['X-RateLimit-Remaining-minute']
                limit = response.headers.get('X-RateLimit-Limit-minute', 'unknown')
                logger.debug(f"Rate limit: {remaining}/{limit} requests remaining this minute")
            
            return response
            
        except requests.RequestException as e:
            logger.error(f"Authenticated request failed: {e}")
            raise
    
    def refresh_token_if_needed(self, jwt_generator, force: bool = False) -> bool:
        """
        Refresh the access token if needed or forced.
        
        Args:
            jwt_generator: JWT generator instance to create new JWT tokens
            force: Force refresh even if current token is valid
            
        Returns:
            True if token was refreshed, False otherwise
        """
        if force or not self.is_token_valid():
            try:
                logger.info("Refreshing access token...")
                jwt_token = jwt_generator.generate_token()
                self.exchange_token(jwt_token)
                logger.info("Access token refreshed successfully")
                return True
            except Exception as e:
                logger.error(f"Failed to refresh access token: {e}")
                raise
        
        return False


def main():
    """Test the OAuth client."""
    import sys
    import os
    
    # Add src to path for imports
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    
    from src.jwt_generator import JWTGenerator
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Test configuration
    private_key_path = "private.pem"
    app_id = "pure1:apikey:cajETP0O7YI55zQx"
    
    try:
        # Create JWT generator and OAuth client
        jwt_gen = JWTGenerator(private_key_path, app_id)
        oauth_client = OAuthClient()
        
        # Generate JWT token
        jwt_token = jwt_gen.generate_token()
        print(f"Generated JWT token: {jwt_token[:50]}...")
        
        # Exchange for access token
        token_data = oauth_client.exchange_token(jwt_token)
        print(f"Access token obtained: {token_data['access_token'][:50]}...")
        print(f"Token type: {token_data['token_type']}")
        print(f"Expires in: {token_data['expires_in']} seconds")
        
        # Get token info
        info = oauth_client.get_token_info()
        print(f"Token info: {info}")
        
        # Test auth headers
        headers = oauth_client.get_auth_headers()
        print(f"Auth headers ready: {list(headers.keys())}")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
