# PureStorage Health Monitor Environment Variables
# Copy this file to .env and update with your values

# PureStorage Configuration
PURESTORAGE__APP_ID=pure1:apikey:cajETP0O7YI55zQx
PURESTORAGE__PRIVATE_KEY_PATH=private.pem
PURESTORAGE__PUBLIC_KEY_PATH=public.pem
PURESTORAGE__PRIVATE_KEY_PASSPHRASE=
PURESTORAGE__API_BASE_URL=https://api.pure1.purestorage.com
PURESTORAGE__TOKEN_EXPIRY_DAYS=30

# OTLP Configuration
OTLP__ENABLED=true
OTLP__ENDPOINT=http://localhost:4317
OTLP__HEADERS={}
OTLP__TIMEOUT=30
OTLP__SERVICE_NAME=purestorage-health-monitor

# Log Reporting Configuration
LOG_REPORTING__ENABLED=false
LOG_REPORTING__LEVEL=INFO

# Monitoring Configuration
MONITORING__COLLECTION_INTERVAL=300
MONITORING__MAX_ALERTS=1000
MONITORING__MAX_ARRAYS=100
MONITORING__MAX_HARDWARE=500
MONITORING__MAX_DRIVES=1000
MONITORING__MAX_CONTROLLERS=200
MONITORING__MAX_VOLUMES=500
MONITORING__ALERT_SEVERITIES=["critical","warning"]
MONITORING__ALERT_STATES=["open"]
MONITORING__METRICS_ENABLED=true
MONITORING__METRICS_RESOLUTION=300000
MONITORING__METRICS_HISTORY_HOURS=24

# Logging Configuration
LOGGING__LEVEL=INFO
LOGGING__FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOGGING__FILE=purestorage_monitor.log
LOGGING__MAX_SIZE=10485760
LOGGING__BACKUP_COUNT=5
